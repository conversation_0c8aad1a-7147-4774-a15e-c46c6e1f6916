<template>
  <div class="demo-data">
    <h4>演示数据控制</h4>
    
    <div class="control-group">
      <label>
        <input 
          type="checkbox" 
          v-model="isSimulating" 
          @change="toggleSimulation"
        >
        启用数据模拟
      </label>
    </div>
    
    <div v-if="isSimulating" class="simulation-controls">
      <div class="control-item">
        <label>更新频率 (ms):</label>
        <input 
          type="number" 
          v-model.number="updateInterval" 
          @change="restartSimulation"
          min="100"
          max="5000"
          step="100"
        >
      </div>
      
      <div class="data-sources">
        <h5>数据源状态</h5>
        <div v-for="(data, sourceId) in mockData" :key="sourceId" class="source-item">
          <strong>{{ sourceId }}:</strong>
          <pre>{{ JSON.stringify(data, null, 2) }}</pre>
        </div>
      </div>
    </div>
    
    <div class="actions">
      <button @click="addSampleNodes" class="action-btn">添加示例节点</button>
      <button @click="clearCanvas" class="action-btn danger">清空画布</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useDataSourceStore } from '@/stores/dataSource'
import { dataBindingService } from '@/services/DataBindingService'
import type { NodeElement, DataSource } from '@/types'

const canvasStore = useCanvasStore()
const dataSourceStore = useDataSourceStore()

const isSimulating = ref(false)
const updateInterval = ref(1000)
const mockData = ref<Record<string, any>>({})
let simulationTimer: number | null = null

// 切换模拟
function toggleSimulation() {
  if (isSimulating.value) {
    startSimulation()
  } else {
    stopSimulation()
  }
}

// 开始模拟
function startSimulation() {
  stopSimulation() // 先停止之前的模拟
  
  simulationTimer = window.setInterval(() => {
    generateMockData()
  }, updateInterval.value)
  
  generateMockData() // 立即生成一次数据
}

// 停止模拟
function stopSimulation() {
  if (simulationTimer) {
    clearInterval(simulationTimer)
    simulationTimer = null
  }
}

// 重启模拟
function restartSimulation() {
  if (isSimulating.value) {
    startSimulation()
  }
}

// 生成模拟数据
function generateMockData() {
  const newMockData = {
    device_01: {
      status: Math.random() > 0.8 ? 'error' : Math.random() > 0.5 ? 'warning' : 'normal',
      temperature: Math.round(Math.random() * 100),
      pressure: Math.round(Math.random() * 50),
      running: Math.random() > 0.3,
      level: Math.round(Math.random() * 100)
    },
    device_02: {
      status: Math.random() > 0.9 ? 'offline' : 'normal',
      flow: Math.round(Math.random() * 200),
      valve_open: Math.random() > 0.5,
      temperature: Math.round(Math.random() * 80)
    },
    system: {
      overall_status: Math.random() > 0.7 ? 'alarm' : 'normal',
      active_alarms: Math.floor(Math.random() * 5),
      total_devices: 12,
      online_devices: Math.floor(Math.random() * 12) + 1
    }
  }
  
  mockData.value = newMockData
  
  // 触发数据更新事件
  Object.entries(newMockData).forEach(([sourceId, data]) => {
    window.dispatchEvent(new CustomEvent('dataUpdate', {
      detail: { sourceId, data }
    }))
  })
}

// 添加示例节点
function addSampleNodes() {
  // 清空现有节点
  canvasStore.nodes = []
  
  // 添加示例数据源
  const sampleDataSources: DataSource[] = [
    {
      id: 'device_01',
      name: '设备01',
      type: 'websocket',
      url: 'ws://localhost:8080/device01'
    },
    {
      id: 'device_02',
      name: '设备02',
      type: 'websocket',
      url: 'ws://localhost:8080/device02'
    },
    {
      id: 'system',
      name: '系统状态',
      type: 'http',
      url: 'http://localhost:8080/api/system/status'
    }
  ]
  
  dataSourceStore.dataSources = sampleDataSources
  
  // 添加示例节点
  const sampleNodes: Omit<NodeElement, 'id'>[] = [
    {
      name: '温度传感器',
      type: 'circle',
      position: { x: 100, y: 100 },
      size: { width: 60, height: 60 },
      style: { fill: '#4caf50', stroke: '#388e3c', strokeWidth: 2 },
      zIndex: 1,
      dataBind: { source: 'device_01', field: 'status' }
    },
    {
      name: '压力表',
      type: 'rect',
      position: { x: 200, y: 100 },
      size: { width: 80, height: 50 },
      style: { fill: '#2196f3', stroke: '#1976d2', strokeWidth: 2 },
      zIndex: 2,
      dataBind: { source: 'device_01', field: 'pressure' }
    },
    {
      name: '流量计',
      type: 'circle',
      position: { x: 320, y: 100 },
      size: { width: 70, height: 70 },
      style: { fill: '#ff9800', stroke: '#f57c00', strokeWidth: 2 },
      zIndex: 3,
      dataBind: { source: 'device_02', field: 'flow' }
    },
    {
      name: '管道',
      type: 'line',
      position: { x: 100, y: 200 },
      size: { width: 300, height: 4 },
      style: { stroke: '#666', strokeWidth: 4 },
      zIndex: 0
    },
    {
      name: '控制阀',
      type: 'rect',
      position: { x: 250, y: 180 },
      size: { width: 40, height: 40 },
      style: { fill: '#9c27b0', stroke: '#7b1fa2', strokeWidth: 2 },
      zIndex: 4,
      dataBind: { source: 'device_02', field: 'valve_open' }
    },
    {
      name: '系统状态指示器',
      type: 'rect',
      position: { x: 450, y: 50 },
      size: { width: 100, height: 30 },
      style: { fill: '#4caf50', stroke: '#388e3c', strokeWidth: 1 },
      zIndex: 5,
      dataBind: { source: 'system', field: 'overall_status' }
    }
  ]
  
  sampleNodes.forEach(node => {
    canvasStore.addNode(node)
  })
}

// 清空画布
function clearCanvas() {
  if (confirm('确定要清空画布吗？')) {
    canvasStore.nodes = []
    canvasStore.selectedNodeIds = []
  }
}

onMounted(() => {
  // 初始化时添加示例节点
  if (canvasStore.nodes.length === 0) {
    addSampleNodes()
  }
})

onUnmounted(() => {
  stopSimulation()
})
</script>

<style scoped>
.demo-data {
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  margin: 16px;
}

.control-group {
  margin-bottom: 16px;
}

.control-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.simulation-controls {
  background: white;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.control-item {
  margin-bottom: 12px;
}

.control-item label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
}

.control-item input {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.data-sources h5 {
  margin: 12px 0 8px 0;
  font-size: 13px;
  color: #333;
}

.source-item {
  margin-bottom: 8px;
  font-size: 11px;
}

.source-item strong {
  color: #2196f3;
}

.source-item pre {
  background: #f5f5f5;
  padding: 4px;
  border-radius: 2px;
  margin: 4px 0;
  font-size: 10px;
  overflow-x: auto;
}

.actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  background: #2196f3;
  color: white;
}

.action-btn:hover {
  background: #1976d2;
}

.action-btn.danger {
  background: #f44336;
}

.action-btn.danger:hover {
  background: #d32f2f;
}
</style>
