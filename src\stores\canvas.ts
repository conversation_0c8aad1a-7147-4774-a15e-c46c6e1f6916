import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { NodeElement, Position, CanvasState } from '@/types'

export const useCanvasStore = defineStore('canvas', () => {
  // 状态
  const nodes = ref<NodeElement[]>([])
  const selectedNodeIds = ref<string[]>([])
  const scale = ref(1)
  const offset = ref<Position>({ x: 0, y: 0 })
  const gridSize = ref(20)
  const showGrid = ref(true)
  const nextNodeId = ref(1)

  // 计算属性
  const selectedNodes = computed(() => 
    nodes.value.filter(node => selectedNodeIds.value.includes(node.id))
  )

  const canvasState = computed<CanvasState>(() => ({
    nodes: nodes.value,
    selectedNodeIds: selectedNodeIds.value,
    scale: scale.value,
    offset: offset.value,
    gridSize: gridSize.value,
    showGrid: showGrid.value
  }))

  // 方法
  function addNode(node: Omit<NodeElement, 'id'>) {
    const newNode: NodeElement = {
      ...node,
      id: `node_${nextNodeId.value++}`
    }
    nodes.value.push(newNode)
    return newNode.id
  }

  function updateNode(id: string, updates: Partial<NodeElement>) {
    const index = nodes.value.findIndex(node => node.id === id)
    if (index !== -1) {
      nodes.value[index] = { ...nodes.value[index], ...updates }
    }
  }

  function deleteNode(id: string) {
    const index = nodes.value.findIndex(node => node.id === id)
    if (index !== -1) {
      nodes.value.splice(index, 1)
      selectedNodeIds.value = selectedNodeIds.value.filter(nodeId => nodeId !== id)
    }
  }

  function selectNode(id: string, multiple = false) {
    if (multiple) {
      if (selectedNodeIds.value.includes(id)) {
        selectedNodeIds.value = selectedNodeIds.value.filter(nodeId => nodeId !== id)
      } else {
        selectedNodeIds.value.push(id)
      }
    } else {
      selectedNodeIds.value = [id]
    }
  }

  function clearSelection() {
    selectedNodeIds.value = []
  }

  function setScale(newScale: number) {
    scale.value = Math.max(0.1, Math.min(5, newScale))
  }

  function setOffset(newOffset: Position) {
    offset.value = newOffset
  }

  function exportToJSON(): string {
    return JSON.stringify(canvasState.value, null, 2)
  }

  function importFromJSON(jsonData: string) {
    try {
      const data = JSON.parse(jsonData) as CanvasState
      nodes.value = data.nodes
      selectedNodeIds.value = data.selectedNodeIds
      scale.value = data.scale
      offset.value = data.offset
      gridSize.value = data.gridSize
      showGrid.value = data.showGrid
    } catch (error) {
      console.error('Failed to import JSON:', error)
    }
  }

  return {
    // 状态
    nodes,
    selectedNodeIds,
    scale,
    offset,
    gridSize,
    showGrid,
    // 计算属性
    selectedNodes,
    canvasState,
    // 方法
    addNode,
    updateNode,
    deleteNode,
    selectNode,
    clearSelection,
    setScale,
    setOffset,
    exportToJSON,
    importFromJSON
  }
})
