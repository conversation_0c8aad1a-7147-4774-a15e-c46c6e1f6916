# 📘 Le5le 可视化组态系统（Meta2D平台）

基于 Vue3 + TypeScript + Pinia + Vite 构建的 2D 可视化组态平台，支持图元拖拽、实时数据绑定、动画联动、事件交互、自定义组件与多端部署。

## 🚀 功能特性

### 🖼️ 编辑器功能
- ✅ 拖拽式画布：图元拖放、缩放、旋转、组合、复制粘贴
- ✅ 图层控制：Z 轴排序、锁定/隐藏/命名
- ✅ 属性面板：位置、颜色、大小、动画、事件等实时编辑
- ✅ 快捷键支持：撤销、重做、对齐、组合等操作
- ✅ 网格辅助线：可吸附、网格间距设置

### 📡 数据绑定
- ✅ WebSocket 实时双向通信
- ✅ HTTP RESTful 查询
- ✅ 属性绑定（位置/颜色/旋转等）
- ✅ 状态绑定（运行、告警、故障等）
- ✅ 动画绑定（数值驱动）

### 🔄 联动与事件
- ✅ 图元事件触发：点击、悬停、状态切换
- ✅ 联动响应：弹窗、跳转、图元高亮、样式变更
- ✅ 事件配置器：支持可视化事件配置

### 🧱 图元库
- ✅ 基础图形：矩形、圆形、线条、箭头
- ✅ 控件组件：按钮、指示灯、仪表盘、进度条
- ✅ 自定义图元：支持 SVG/PNG/JSON 格式上传

### 📤 导出部署
- ✅ JSON 配置文件导出
- ✅ HTML 静态文件导出
- ✅ Vue 组件导出
- ✅ React 组件导出
- ✅ 离线运行包部署

## 🛠️ 技术栈

- **前端框架**: Vue 3 + TypeScript + Composition API
- **状态管理**: Pinia
- **构建工具**: Vite
- **路由管理**: Vue Router
- **图形渲染**: Canvas API + SVG
- **动画引擎**: GSAP（可选）
- **通信协议**: WebSocket / HTTP REST API

## 📁 项目结构

```
src/
├── components/         # 组件库
│   ├── Canvas/        # 画布相关组件
│   ├── Toolbar/       # 工具栏组件
│   ├── Properties/    # 属性面板组件
│   └── Demo/          # 演示组件
├── services/          # 服务层
│   ├── DataBindingService.ts    # 数据绑定服务
│   ├── EventService.ts          # 事件处理服务
│   └── ExportService.ts         # 导出服务
├── stores/            # Pinia 状态管理
│   ├── canvas.ts      # 画布状态
│   └── dataSource.ts  # 数据源状态
├── types/             # TypeScript 类型定义
├── views/             # 页面组件
└── App.vue           # 根组件
```

## 🚀 快速开始

### 环境要求
- Node.js >= 20.19.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 类型检查
```bash
npm run type-check
```
