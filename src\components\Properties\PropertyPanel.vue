<template>
  <div class="property-panel">
    <h3>属性面板</h3>
    
    <div v-if="selectedNodes.length === 0" class="no-selection">
      <p>请选择一个图元</p>
    </div>
    
    <div v-else-if="selectedNodes.length === 1" class="single-selection">
      <div class="property-group">
        <h4>基本属性</h4>
        
        <!-- 名称 -->
        <div class="property-item">
          <label>名称</label>
          <input 
            v-model="currentNode.name" 
            @input="updateProperty('name', currentNode.name)"
            type="text"
          >
        </div>
        
        <!-- 位置 -->
        <div class="property-item">
          <label>位置</label>
          <div class="input-group">
            <input 
              v-model.number="currentNode.position.x" 
              @input="updatePosition"
              type="number"
              placeholder="X"
            >
            <input 
              v-model.number="currentNode.position.y" 
              @input="updatePosition"
              type="number"
              placeholder="Y"
            >
          </div>
        </div>
        
        <!-- 尺寸 -->
        <div class="property-item">
          <label>尺寸</label>
          <div class="input-group">
            <input 
              v-model.number="currentNode.size.width" 
              @input="updateSize"
              type="number"
              placeholder="宽度"
            >
            <input 
              v-model.number="currentNode.size.height" 
              @input="updateSize"
              type="number"
              placeholder="高度"
            >
          </div>
        </div>
        
        <!-- 旋转 -->
        <div class="property-item">
          <label>旋转角度</label>
          <input 
            v-model.number="currentNode.rotation" 
            @input="updateProperty('rotation', currentNode.rotation || 0)"
            type="number"
            min="0"
            max="360"
          >
        </div>
        
        <!-- Z轴层级 -->
        <div class="property-item">
          <label>层级</label>
          <input 
            v-model.number="currentNode.zIndex" 
            @input="updateProperty('zIndex', currentNode.zIndex)"
            type="number"
          >
        </div>
      </div>
      
      <!-- 样式属性 -->
      <div class="property-group">
        <h4>样式属性</h4>
        
        <!-- 填充颜色 -->
        <div class="property-item">
          <label>填充颜色</label>
          <div class="color-input">
            <input 
              v-model="currentNode.style.fill" 
              @input="updateStyle"
              type="color"
            >
            <input 
              v-model="currentNode.style.fill" 
              @input="updateStyle"
              type="text"
              placeholder="#ffffff"
            >
          </div>
        </div>
        
        <!-- 边框颜色 -->
        <div class="property-item">
          <label>边框颜色</label>
          <div class="color-input">
            <input 
              v-model="currentNode.style.stroke" 
              @input="updateStyle"
              type="color"
            >
            <input 
              v-model="currentNode.style.stroke" 
              @input="updateStyle"
              type="text"
              placeholder="#000000"
            >
          </div>
        </div>
        
        <!-- 边框宽度 -->
        <div class="property-item">
          <label>边框宽度</label>
          <input 
            v-model.number="currentNode.style.strokeWidth" 
            @input="updateStyle"
            type="number"
            min="0"
          >
        </div>
        
        <!-- 透明度 -->
        <div class="property-item">
          <label>透明度</label>
          <input 
            v-model.number="currentNode.style.opacity" 
            @input="updateStyle"
            type="range"
            min="0"
            max="1"
            step="0.1"
          >
          <span>{{ (currentNode.style.opacity || 1).toFixed(1) }}</span>
        </div>
      </div>
      
      <!-- 数据绑定 -->
      <div class="property-group">
        <h4>数据绑定</h4>
        
        <div class="property-item">
          <label>数据源</label>
          <select v-model="dataBindSource" @change="updateDataBind">
            <option value="">无</option>
            <option v-for="source in dataSources" :key="source.id" :value="source.id">
              {{ source.name }}
            </option>
          </select>
        </div>
        
        <div v-if="dataBindSource" class="property-item">
          <label>字段</label>
          <input 
            v-model="dataBindField" 
            @input="updateDataBind"
            type="text"
            placeholder="例如: temperature"
          >
        </div>
      </div>
      
      <!-- 事件配置 -->
      <div class="property-group">
        <h4>事件配置</h4>
        
        <div class="property-item">
          <label>点击事件</label>
          <select v-model="clickEventType" @change="updateEvents">
            <option value="">无</option>
            <option value="openModal">打开弹窗</option>
            <option value="navigate">页面跳转</option>
            <option value="highlight">高亮其他图元</option>
          </select>
        </div>
        
        <div v-if="clickEventType" class="property-item">
          <label>目标</label>
          <input 
            v-model="clickEventTarget" 
            @input="updateEvents"
            type="text"
            placeholder="目标ID或URL"
          >
        </div>
      </div>
    </div>
    
    <div v-else class="multi-selection">
      <p>已选择 {{ selectedNodes.length }} 个图元</p>
      <button @click="alignNodes('left')" class="align-btn">左对齐</button>
      <button @click="alignNodes('center')" class="align-btn">居中对齐</button>
      <button @click="alignNodes('right')" class="align-btn">右对齐</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useDataSourceStore } from '@/stores/dataSource'
import type { NodeElement } from '@/types'

const canvasStore = useCanvasStore()
const dataSourceStore = useDataSourceStore()

const selectedNodes = computed(() => canvasStore.selectedNodes)
const dataSources = computed(() => dataSourceStore.dataSources)

// 当前编辑的节点（单选时）
const currentNode = ref<NodeElement | null>(null)
const dataBindSource = ref('')
const dataBindField = ref('')
const clickEventType = ref('')
const clickEventTarget = ref('')

// 监听选择变化
watch(selectedNodes, (nodes) => {
  if (nodes.length === 1) {
    currentNode.value = { ...nodes[0] }
    // 初始化数据绑定信息
    if (currentNode.value.dataBind) {
      dataBindSource.value = currentNode.value.dataBind.source
      dataBindField.value = currentNode.value.dataBind.field
    } else {
      dataBindSource.value = ''
      dataBindField.value = ''
    }
    // 初始化事件信息
    if (currentNode.value.events?.onClick) {
      clickEventType.value = currentNode.value.events.onClick.type
      clickEventTarget.value = currentNode.value.events.onClick.target || ''
    } else {
      clickEventType.value = ''
      clickEventTarget.value = ''
    }
  } else {
    currentNode.value = null
  }
}, { immediate: true })

// 更新属性
function updateProperty(key: keyof NodeElement, value: any) {
  if (!currentNode.value) return
  canvasStore.updateNode(currentNode.value.id, { [key]: value })
}

function updatePosition() {
  if (!currentNode.value) return
  updateProperty('position', currentNode.value.position)
}

function updateSize() {
  if (!currentNode.value) return
  updateProperty('size', currentNode.value.size)
}

function updateStyle() {
  if (!currentNode.value) return
  updateProperty('style', currentNode.value.style)
}

function updateDataBind() {
  if (!currentNode.value) return
  
  if (dataBindSource.value && dataBindField.value) {
    updateProperty('dataBind', {
      source: dataBindSource.value,
      field: dataBindField.value
    })
  } else {
    updateProperty('dataBind', undefined)
  }
}

function updateEvents() {
  if (!currentNode.value) return
  
  const events: any = {}
  
  if (clickEventType.value) {
    events.onClick = {
      type: clickEventType.value,
      target: clickEventTarget.value
    }
  }
  
  updateProperty('events', Object.keys(events).length > 0 ? events : undefined)
}

// 对齐功能
function alignNodes(type: 'left' | 'center' | 'right') {
  const nodes = selectedNodes.value
  if (nodes.length < 2) return
  
  let targetX: number
  
  switch (type) {
    case 'left':
      targetX = Math.min(...nodes.map(n => n.position.x))
      break
    case 'center':
      const minX = Math.min(...nodes.map(n => n.position.x))
      const maxX = Math.max(...nodes.map(n => n.position.x + n.size.width))
      targetX = (minX + maxX) / 2
      break
    case 'right':
      targetX = Math.max(...nodes.map(n => n.position.x + n.size.width))
      break
  }
  
  nodes.forEach(node => {
    let newX = targetX
    if (type === 'center') {
      newX = targetX - node.size.width / 2
    } else if (type === 'right') {
      newX = targetX - node.size.width
    }
    
    canvasStore.updateNode(node.id, {
      position: { x: newX, y: node.position.y }
    })
  })
}
</script>

<style scoped>
.property-panel {
  width: 300px;
  height: 100%;
  background: #fff;
  border-left: 1px solid #e0e0e0;
  padding: 16px;
  overflow-y: auto;
}

.no-selection, .multi-selection {
  text-align: center;
  color: #666;
  padding: 20px;
}

.property-group {
  margin-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.property-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.property-item {
  margin-bottom: 12px;
}

.property-item label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
}

.property-item input,
.property-item select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

.input-group {
  display: flex;
  gap: 8px;
}

.input-group input {
  flex: 1;
}

.color-input {
  display: flex;
  gap: 8px;
  align-items: center;
}

.color-input input[type="color"] {
  width: 40px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.color-input input[type="text"] {
  flex: 1;
}

.property-item input[type="range"] {
  flex: 1;
}

.property-item span {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.align-btn {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  padding: 8px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.align-btn:hover {
  background: #1976d2;
}
</style>
