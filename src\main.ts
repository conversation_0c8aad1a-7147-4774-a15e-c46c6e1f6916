import './assets/main.css'
import './utils/devtools'

import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { pinia } from './stores'
import App from './App.vue'
import EditorView from './views/EditorView.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'editor',
      component: EditorView
    }
  ]
})

const app = createApp(App)
app.use(pinia)
app.use(router)
app.mount('#app')
