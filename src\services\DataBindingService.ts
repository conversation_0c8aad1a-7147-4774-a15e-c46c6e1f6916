import { useCanvasStore } from '@/stores/canvas'
import { useDataSourceStore } from '@/stores/dataSource'
import type { NodeElement } from '@/types'

export class DataBindingService {
  // Remove store initialization from class level to avoid Pinia error
  private get canvasStore() {
    return useCanvasStore()
  }

  private get dataSourceStore() {
    return useDataSourceStore()
  }

  private updateInterval: number | null = null

  constructor() {
    this.startDataBinding()
    this.setupEventListeners()
  }

  // 启动数据绑定服务
  startDataBinding() {
    // 每30ms检查一次数据更新
    this.updateInterval = window.setInterval(() => {
      this.updateBoundNodes()
    }, 30)
  }

  // 停止数据绑定服务
  stopDataBinding() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
  }

  // 设置事件监听器
  setupEventListeners() {
    window.addEventListener('dataUpdate', this.handleDataUpdate.bind(this))
  }

  // 处理数据更新事件
  handleDataUpdate(event: CustomEvent) {
    const { sourceId, data } = event.detail
    this.updateNodesForSource(sourceId, data)
  }

  // 更新绑定的节点
  updateBoundNodes() {
    this.canvasStore.nodes.forEach(node => {
      if (node.dataBind) {
        const data = this.dataSourceStore.getData(node.dataBind.source, node.dataBind.field)
        if (data !== null) {
          this.applyDataToNode(node, data)
        }
      }
    })
  }

  // 为特定数据源更新节点
  updateNodesForSource(sourceId: string, data: any) {
    this.canvasStore.nodes.forEach(node => {
      if (node.dataBind?.source === sourceId) {
        const fieldData = this.getFieldValue(data, node.dataBind.field)
        if (fieldData !== null) {
          this.applyDataToNode(node, fieldData)
        }
      }
    })
  }

  // 将数据应用到节点
  applyDataToNode(node: NodeElement, data: any) {
    const updates: Partial<NodeElement> = {}

    // 根据数据类型和节点类型应用不同的绑定逻辑
    if (typeof data === 'number') {
      // 数值数据可以绑定到位置、大小、旋转等
      if (node.type === 'gauge') {
        // 仪表盘显示数值
        updates.style = {
          ...node.style,
          // 可以根据数值改变颜色或其他样式
        }
      }
    } else if (typeof data === 'string') {
      // 字符串数据可以绑定到状态
      switch (data.toLowerCase()) {
        case 'error':
        case 'alarm':
          updates.style = {
            ...node.style,
            fill: '#f44336', // 红色表示错误
            stroke: '#d32f2f'
          }
          break
        case 'warning':
          updates.style = {
            ...node.style,
            fill: '#ff9800', // 橙色表示警告
            stroke: '#f57c00'
          }
          break
        case 'normal':
        case 'ok':
          updates.style = {
            ...node.style,
            fill: '#4caf50', // 绿色表示正常
            stroke: '#388e3c'
          }
          break
        case 'offline':
          updates.style = {
            ...node.style,
            fill: '#9e9e9e', // 灰色表示离线
            stroke: '#616161',
            opacity: 0.5
          }
          break
      }
    } else if (typeof data === 'boolean') {
      // 布尔值可以控制显示/隐藏或开/关状态
      if (node.type === 'indicator') {
        updates.style = {
          ...node.style,
          fill: data ? '#4caf50' : '#f44336'
        }
      }
      updates.visible = data
    } else if (typeof data === 'object' && data !== null) {
      // 对象数据可以包含位置、颜色等复合信息
      if (data.position) {
        updates.position = {
          x: data.position.x || node.position.x,
          y: data.position.y || node.position.y
        }
      }
      if (data.color) {
        updates.style = {
          ...node.style,
          fill: data.color
        }
      }
      if (data.size) {
        updates.size = {
          width: data.size.width || node.size.width,
          height: data.size.height || node.size.height
        }
      }
    }

    // 应用更新
    if (Object.keys(updates).length > 0) {
      this.canvasStore.updateNode(node.id, updates)
    }
  }

  // 获取嵌套字段值
  getFieldValue(data: any, field: string): any {
    if (!field) return data
    
    return field.split('.').reduce((obj, key) => {
      return obj && obj[key] !== undefined ? obj[key] : null
    }, data)
  }

  // 模拟数据推送（用于测试）
  simulateDataPush() {
    const mockData = {
      device_01: {
        status: Math.random() > 0.8 ? 'error' : Math.random() > 0.5 ? 'warning' : 'normal',
        temperature: Math.round(Math.random() * 100),
        pressure: Math.round(Math.random() * 50),
        running: Math.random() > 0.3
      },
      device_02: {
        status: Math.random() > 0.9 ? 'offline' : 'normal',
        level: Math.round(Math.random() * 100),
        flow: Math.round(Math.random() * 200)
      }
    }

    // 触发数据更新事件
    Object.entries(mockData).forEach(([deviceId, data]) => {
      window.dispatchEvent(new CustomEvent('dataUpdate', {
        detail: { sourceId: deviceId, data }
      }))
    })
  }

  // 创建WebSocket连接
  createWebSocketConnection(url: string, sourceId: string) {
    const ws = new WebSocket(url)
    
    ws.onopen = () => {
      console.log(`WebSocket connected: ${sourceId}`)
    }

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.dataSourceStore.dataCache.set(sourceId, data)
        
        // 触发数据更新事件
        window.dispatchEvent(new CustomEvent('dataUpdate', {
          detail: { sourceId, data }
        }))
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    }

    ws.onclose = () => {
      console.log(`WebSocket disconnected: ${sourceId}`)
      // 可以实现重连逻辑
    }

    ws.onerror = (error) => {
      console.error(`WebSocket error for ${sourceId}:`, error)
    }

    return ws
  }

  // 创建MQTT连接（需要MQTT.js库）
  createMQTTConnection(url: string, topic: string, sourceId: string) {
    // 这里需要引入MQTT.js库
    // import mqtt from 'mqtt'
    // const client = mqtt.connect(url)
    // 
    // client.on('connect', () => {
    //   client.subscribe(topic)
    // })
    // 
    // client.on('message', (topic, message) => {
    //   try {
    //     const data = JSON.parse(message.toString())
    //     this.handleDataUpdate({ detail: { sourceId, data } } as CustomEvent)
    //   } catch (error) {
    //     console.error('Failed to parse MQTT message:', error)
    //   }
    // })
    
    console.log('MQTT connection would be created here')
  }

  // HTTP轮询
  async createHTTPPolling(url: string, sourceId: string, interval = 5000) {
    const poll = async () => {
      try {
        const response = await fetch(url)
        const data = await response.json()
        this.dataSourceStore.dataCache.set(sourceId, data)
        
        window.dispatchEvent(new CustomEvent('dataUpdate', {
          detail: { sourceId, data }
        }))
      } catch (error) {
        console.error(`HTTP polling error for ${sourceId}:`, error)
      }
    }

    // 立即执行一次
    await poll()
    
    // 设置定时轮询
    return setInterval(poll, interval)
  }
}

// 创建全局实例
export const dataBindingService = new DataBindingService()
