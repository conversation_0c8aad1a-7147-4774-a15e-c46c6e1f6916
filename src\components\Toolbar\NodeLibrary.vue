<template>
  <div class="node-library">
    <h3>图元库</h3>
    
    <!-- 基础图形 -->
    <div class="category">
      <h4>基础图形</h4>
      <div class="node-grid">
        <div 
          v-for="shape in basicShapes" 
          :key="shape.type"
          class="node-item"
          :draggable="true"
          @dragstart="handleDragStart(shape, $event)"
          @click="addNode(shape)"
        >
          <div class="node-preview" :class="shape.type">
            <div v-if="shape.type === 'rect'" class="preview-rect"></div>
            <div v-else-if="shape.type === 'circle'" class="preview-circle"></div>
            <svg v-else-if="shape.type === 'line'" class="preview-line">
              <line x1="10%" y1="50%" x2="90%" y2="50%" stroke="#333" stroke-width="2"/>
            </svg>
          </div>
          <span class="node-label">{{ shape.name }}</span>
        </div>
      </div>
    </div>
    
    <!-- 控件组件 -->
    <div class="category">
      <h4>控件组件</h4>
      <div class="node-grid">
        <div 
          v-for="widget in widgets" 
          :key="widget.type"
          class="node-item"
          :draggable="true"
          @dragstart="handleDragStart(widget, $event)"
          @click="addNode(widget)"
        >
          <div class="node-preview widget">
            <div v-if="widget.type === 'button'" class="preview-button">按钮</div>
            <div v-else-if="widget.type === 'indicator'" class="preview-indicator"></div>
            <div v-else-if="widget.type === 'gauge'" class="preview-gauge">
              <div class="gauge-arc"></div>
            </div>
          </div>
          <span class="node-label">{{ widget.name }}</span>
        </div>
      </div>
    </div>
    
    <!-- 自定义图元 -->
    <div class="category">
      <h4>自定义图元</h4>
      <div class="upload-area" @click="handleUpload">
        <div class="upload-icon">+</div>
        <span>上传图元</span>
      </div>
      <input 
        ref="fileInput" 
        type="file" 
        accept=".svg,.png,.jpg,.json" 
        style="display: none"
        @change="handleFileUpload"
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import type { NodeElement } from '@/types'

const canvasStore = useCanvasStore()
const fileInput = ref<HTMLInputElement>()

// 基础图形定义
const basicShapes = [
  {
    type: 'rect',
    name: '矩形',
    defaultSize: { width: 100, height: 60 },
    defaultStyle: { fill: '#e3f2fd', stroke: '#1976d2', strokeWidth: 2 }
  },
  {
    type: 'circle',
    name: '圆形',
    defaultSize: { width: 80, height: 80 },
    defaultStyle: { fill: '#f3e5f5', stroke: '#7b1fa2', strokeWidth: 2 }
  },
  {
    type: 'line',
    name: '线条',
    defaultSize: { width: 120, height: 4 },
    defaultStyle: { stroke: '#333', strokeWidth: 2 }
  }
]

// 控件组件定义
const widgets = [
  {
    type: 'button',
    name: '按钮',
    defaultSize: { width: 80, height: 32 },
    defaultStyle: { fill: '#2196f3', stroke: '#1976d2', strokeWidth: 1 }
  },
  {
    type: 'indicator',
    name: '指示灯',
    defaultSize: { width: 24, height: 24 },
    defaultStyle: { fill: '#4caf50', stroke: '#388e3c', strokeWidth: 1 }
  },
  {
    type: 'gauge',
    name: '仪表盘',
    defaultSize: { width: 120, height: 120 },
    defaultStyle: { fill: 'transparent', stroke: '#666', strokeWidth: 2 }
  }
]

// 添加节点到画布
function addNode(template: any) {
  const newNode: Omit<NodeElement, 'id'> = {
    name: template.name,
    type: template.type,
    position: { x: 100, y: 100 },
    size: template.defaultSize,
    style: template.defaultStyle,
    zIndex: canvasStore.nodes.length + 1
  }
  
  canvasStore.addNode(newNode)
}

// 拖拽开始
function handleDragStart(template: any, event: DragEvent) {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(template))
    event.dataTransfer.effectAllowed = 'copy'
  }
}

// 上传文件
function handleUpload() {
  fileInput.value?.click()
}

function handleFileUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    const result = e.target?.result as string
    
    if (file.type === 'application/json') {
      try {
        const customNode = JSON.parse(result)
        addNode(customNode)
      } catch (error) {
        console.error('Invalid JSON file:', error)
      }
    } else {
      // 处理图片文件
      const newNode: Omit<NodeElement, 'id'> = {
        name: file.name,
        type: 'custom',
        position: { x: 100, y: 100 },
        size: { width: 100, height: 100 },
        style: { 
          fill: `url(${result})`,
          stroke: 'transparent',
          strokeWidth: 0
        },
        zIndex: canvasStore.nodes.length + 1
      }
      canvasStore.addNode(newNode)
    }
  }
  
  if (file.type === 'application/json') {
    reader.readAsText(file)
  } else {
    reader.readAsDataURL(file)
  }
}
</script>

<style scoped>
.node-library {
  width: 250px;
  height: 100%;
  background: #fff;
  border-right: 1px solid #e0e0e0;
  padding: 16px;
  overflow-y: auto;
}

.category {
  margin-bottom: 24px;
}

.category h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.node-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.node-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.node-item:hover {
  border-color: #2196f3;
  background: #f5f5f5;
}

.node-preview {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.preview-rect {
  width: 30px;
  height: 20px;
  background: #e3f2fd;
  border: 2px solid #1976d2;
}

.preview-circle {
  width: 24px;
  height: 24px;
  background: #f3e5f5;
  border: 2px solid #7b1fa2;
  border-radius: 50%;
}

.preview-line {
  width: 30px;
  height: 4px;
}

.preview-button {
  background: #2196f3;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
}

.preview-indicator {
  width: 16px;
  height: 16px;
  background: #4caf50;
  border-radius: 50%;
  border: 1px solid #388e3c;
}

.preview-gauge {
  width: 30px;
  height: 30px;
  position: relative;
}

.gauge-arc {
  width: 100%;
  height: 100%;
  border: 2px solid #666;
  border-radius: 50%;
  border-top-color: #2196f3;
}

.node-label {
  font-size: 12px;
  color: #333;
  text-align: center;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 2px dashed #ccc;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.upload-area:hover {
  border-color: #2196f3;
  background: #f5f5f5;
}

.upload-icon {
  font-size: 24px;
  color: #666;
  margin-bottom: 4px;
}
</style>
