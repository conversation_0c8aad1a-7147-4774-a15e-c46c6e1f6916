import { useCanvasStore } from '@/stores/canvas'
import type { NodeElement, EventAction } from '@/types'

export class EventService {
  // Remove store initialization from class level to avoid Pinia error
  private get canvasStore() {
    return useCanvasStore()
  }

  private modalContainer: HTMLElement | null = null

  constructor() {
    this.createModalContainer()
  }

  // 创建模态框容器
  createModalContainer() {
    this.modalContainer = document.createElement('div')
    this.modalContainer.id = 'modal-container'
    this.modalContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    `
    document.body.appendChild(this.modalContainer)
  }

  // 处理节点点击事件
  handleNodeClick(node: NodeElement, event: MouseEvent) {
    if (node.events?.onClick) {
      this.executeAction(node.events.onClick, node, event)
    }
  }

  // 处理节点悬停事件
  handleNodeHover(node: NodeElement, event: MouseEvent) {
    if (node.events?.onHover) {
      this.executeAction(node.events.onHover, node, event)
    }
  }

  // 处理数值变化事件
  handleValueChange(node: NodeElement, oldValue: any, newValue: any) {
    if (node.events?.onValueChange) {
      this.executeAction(node.events.onValueChange, node, { oldValue, newValue })
    }
  }

  // 执行事件动作
  executeAction(action: EventAction, sourceNode: NodeElement, context: any) {
    switch (action.type) {
      case 'openModal':
        this.openModal(action, sourceNode, context)
        break
      case 'navigate':
        this.navigate(action, sourceNode, context)
        break
      case 'highlight':
        this.highlightNode(action, sourceNode, context)
        break
      case 'changeStyle':
        this.changeNodeStyle(action, sourceNode, context)
        break
      default:
        console.warn('Unknown action type:', action.type)
    }
  }

  // 打开模态框
  openModal(action: EventAction, sourceNode: NodeElement, context: any) {
    if (!this.modalContainer) return

    const modalContent = document.createElement('div')
    modalContent.style.cssText = `
      background: white;
      padding: 20px;
      border-radius: 8px;
      max-width: 500px;
      max-height: 400px;
      overflow-y: auto;
      position: relative;
    `

    // 创建关闭按钮
    const closeBtn = document.createElement('button')
    closeBtn.textContent = '×'
    closeBtn.style.cssText = `
      position: absolute;
      top: 10px;
      right: 15px;
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    `
    closeBtn.onclick = () => this.closeModal()

    // 创建内容
    const content = document.createElement('div')
    content.innerHTML = this.generateModalContent(action, sourceNode, context)

    modalContent.appendChild(closeBtn)
    modalContent.appendChild(content)

    this.modalContainer.innerHTML = ''
    this.modalContainer.appendChild(modalContent)
    this.modalContainer.style.display = 'flex'

    // 点击背景关闭
    this.modalContainer.onclick = (e) => {
      if (e.target === this.modalContainer) {
        this.closeModal()
      }
    }
  }

  // 关闭模态框
  closeModal() {
    if (this.modalContainer) {
      this.modalContainer.style.display = 'none'
    }
  }

  // 生成模态框内容
  generateModalContent(action: EventAction, sourceNode: NodeElement, context: any): string {
    const target = action.target || 'info_panel'
    
    switch (target) {
      case 'info_panel':
        return `
          <h3>节点信息</h3>
          <p><strong>名称:</strong> ${sourceNode.name}</p>
          <p><strong>类型:</strong> ${sourceNode.type}</p>
          <p><strong>位置:</strong> (${sourceNode.position.x}, ${sourceNode.position.y})</p>
          <p><strong>尺寸:</strong> ${sourceNode.size.width} × ${sourceNode.size.height}</p>
          ${sourceNode.dataBind ? `
            <h4>数据绑定</h4>
            <p><strong>数据源:</strong> ${sourceNode.dataBind.source}</p>
            <p><strong>字段:</strong> ${sourceNode.dataBind.field}</p>
          ` : ''}
        `
      case 'device_details':
        return `
          <h3>设备详情</h3>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
            <div>
              <h4>基本信息</h4>
              <p>设备ID: ${sourceNode.id}</p>
              <p>设备名称: ${sourceNode.name}</p>
              <p>设备类型: ${sourceNode.type}</p>
            </div>
            <div>
              <h4>运行状态</h4>
              <p>状态: <span style="color: green;">正常</span></p>
              <p>温度: 45°C</p>
              <p>压力: 2.3 MPa</p>
            </div>
          </div>
          <div style="margin-top: 15px;">
            <h4>操作</h4>
            <button onclick="alert('启动设备')" style="margin-right: 10px;">启动</button>
            <button onclick="alert('停止设备')" style="margin-right: 10px;">停止</button>
            <button onclick="alert('重置设备')">重置</button>
          </div>
        `
      default:
        return `
          <h3>自定义面板</h3>
          <p>目标: ${target}</p>
          <p>源节点: ${sourceNode.name}</p>
          <pre>${JSON.stringify(context, null, 2)}</pre>
        `
    }
  }

  // 页面跳转
  navigate(action: EventAction, sourceNode: NodeElement, context: any) {
    const url = action.target || '#'
    
    if (url.startsWith('http')) {
      // 外部链接
      window.open(url, '_blank')
    } else {
      // 内部路由
      window.location.hash = url
    }
  }

  // 高亮节点
  highlightNode(action: EventAction, sourceNode: NodeElement, context: any) {
    const targetId = action.target
    if (!targetId) return

    // 清除之前的高亮
    this.clearHighlight()

    // 查找目标节点
    const targetNode = this.canvasStore.nodes.find(node => 
      node.id === targetId || node.name === targetId
    )

    if (targetNode) {
      // 添加高亮效果
      this.canvasStore.updateNode(targetNode.id, {
        style: {
          ...targetNode.style,
          stroke: '#ff4444',
          strokeWidth: 4
        }
      })

      // 3秒后移除高亮
      setTimeout(() => {
        this.clearHighlight(targetNode.id)
      }, 3000)
    }
  }

  // 清除高亮
  clearHighlight(nodeId?: string) {
    const nodes = nodeId 
      ? [this.canvasStore.nodes.find(n => n.id === nodeId)].filter(Boolean)
      : this.canvasStore.nodes

    nodes.forEach(node => {
      if (node && node.style.stroke === '#ff4444') {
        this.canvasStore.updateNode(node.id, {
          style: {
            ...node.style,
            stroke: '#000',
            strokeWidth: 1
          }
        })
      }
    })
  }

  // 改变节点样式
  changeNodeStyle(action: EventAction, sourceNode: NodeElement, context: any) {
    const targetId = action.target
    const params = action.params || {}

    if (!targetId) return

    const targetNode = this.canvasStore.nodes.find(node => 
      node.id === targetId || node.name === targetId
    )

    if (targetNode) {
      const styleUpdates: any = {}
      
      // 根据参数更新样式
      if (params.color) {
        styleUpdates.fill = params.color
      }
      if (params.borderColor) {
        styleUpdates.stroke = params.borderColor
      }
      if (params.opacity !== undefined) {
        styleUpdates.opacity = params.opacity
      }

      this.canvasStore.updateNode(targetNode.id, {
        style: {
          ...targetNode.style,
          ...styleUpdates
        }
      })
    }
  }

  // 创建联动规则
  createLinkageRule(sourceNodeId: string, targetNodeId: string, condition: any, action: EventAction) {
    // 这里可以实现更复杂的联动规则
    const sourceNode = this.canvasStore.nodes.find(n => n.id === sourceNodeId)
    const targetNode = this.canvasStore.nodes.find(n => n.id === targetNodeId)

    if (sourceNode && targetNode) {
      // 监听源节点的数据变化
      const checkCondition = (data: any) => {
        let conditionMet = false

        switch (condition.type) {
          case 'equals':
            conditionMet = data === condition.value
            break
          case 'greater':
            conditionMet = data > condition.value
            break
          case 'less':
            conditionMet = data < condition.value
            break
          case 'contains':
            conditionMet = String(data).includes(condition.value)
            break
        }

        if (conditionMet) {
          this.executeAction(action, sourceNode, { data, targetNode })
        }
      }

      // 这里需要与数据绑定服务集成
      return checkCondition
    }
  }

  // 批量操作
  batchOperation(nodeIds: string[], operation: string, params?: any) {
    nodeIds.forEach(nodeId => {
      const node = this.canvasStore.nodes.find(n => n.id === nodeId)
      if (node) {
        switch (operation) {
          case 'hide':
            this.canvasStore.updateNode(nodeId, { visible: false })
            break
          case 'show':
            this.canvasStore.updateNode(nodeId, { visible: true })
            break
          case 'changeColor':
            this.canvasStore.updateNode(nodeId, {
              style: { ...node.style, fill: params?.color || '#ff0000' }
            })
            break
        }
      }
    })
  }
}

// 创建全局实例
export const eventService = new EventService()
