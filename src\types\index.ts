// 核心数据类型定义

export interface Position {
  x: number
  y: number
}

export interface Size {
  width: number
  height: number
}

export interface NodeElement {
  id: string
  name: string
  type: 'rect' | 'circle' | 'line' | 'custom'
  position: Position
  size: Size
  rotation?: number
  style: {
    fill?: string
    stroke?: string
    strokeWidth?: number
    opacity?: number
  }
  dataBind?: {
    field: string
    source: string
  }
  events?: {
    onClick?: EventAction
    onHover?: EventAction
    onValueChange?: EventAction
  }
  zIndex: number
  locked?: boolean
  visible?: boolean
}

export interface EventAction {
  type: 'openModal' | 'navigate' | 'highlight' | 'changeStyle'
  target?: string
  params?: Record<string, any>
}

export interface DataSource {
  id: string
  name: string
  type: 'websocket' | 'mqtt' | 'http'
  url: string
  config?: Record<string, any>
}

export interface CanvasState {
  nodes: NodeElement[]
  selectedNodeIds: string[]
  scale: number
  offset: Position
  gridSize: number
  showGrid: boolean
}

export interface ProjectData {
  id: string
  name: string
  canvas: CanvasState
  dataSources: DataSource[]
  createdAt: string
  updatedAt: string
}
