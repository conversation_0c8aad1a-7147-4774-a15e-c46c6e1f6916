import { useCanvasStore } from '@/stores/canvas'
import { useDataSourceStore } from '@/stores/dataSource'
import type { NodeElement, CanvasState, DataSource } from '@/types'

export class ExportService {
  // Remove store initialization from class level to avoid Pinia error
  private get canvasStore() {
    return useCanvasStore()
  }

  private get dataSourceStore() {
    return useDataSourceStore()
  }

  // 导出为JSON配置文件
  exportToJSON(): string {
    const projectData = {
      version: '1.0.0',
      name: 'Le5le Visual Project',
      canvas: this.canvasStore.canvasState,
      dataSources: this.dataSourceStore.dataSources,
      exportTime: new Date().toISOString()
    }

    return JSON.stringify(projectData, null, 2)
  }

  // 导出为HTML静态文件
  exportToHTML(projectName: string = 'Le5le Visual Project'): string {
    const nodes = this.canvasStore.nodes
    const canvasState = this.canvasStore.canvasState
    
    const svgElements = this.generateSVGElements(nodes)
    const styles = this.generateCSS()
    const scripts = this.generateJavaScript(nodes, canvasState)
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${projectName}</title>
    <style>
        ${styles}
    </style>
</head>
<body>
    <div class="container">
        <h1>${projectName}</h1>
        <div class="canvas-container">
            <svg id="main-canvas" width="100%" height="600" viewBox="0 0 1200 600">
                ${svgElements}
            </svg>
        </div>
        <div class="controls">
            <button onclick="toggleDataSimulation()">切换数据模拟</button>
            <button onclick="exportData()">导出数据</button>
        </div>
    </div>
    
    <script>
        ${scripts}
    </script>
</body>
</html>`
  }

  // 导出为Vue组件
  exportToVueComponent(componentName: string = 'VisualComponent'): string {
    const nodes = this.canvasStore.nodes
    const template = this.generateVueTemplate(nodes)
    const script = this.generateVueScript(nodes, componentName)
    const style = this.generateVueStyle()
    
    return `<template>
${template}
</template>

<script setup lang="ts">
${script}
</script>

<style scoped>
${style}
</style>`
  }

  // 导出为React组件
  exportToReactComponent(componentName: string = 'VisualComponent'): string {
    const nodes = this.canvasStore.nodes
    const jsx = this.generateReactJSX(nodes)
    const hooks = this.generateReactHooks(nodes)
    
    return `import React, { useState, useEffect } from 'react';

const ${componentName}: React.FC = () => {
${hooks}

  return (
${jsx}
  );
};

export default ${componentName};`
  }

  // 生成SVG元素
  private generateSVGElements(nodes: NodeElement[]): string {
    return nodes.map(node => {
      const { position, size, style, type, rotation = 0 } = node
      const transform = rotation ? ` transform="rotate(${rotation} ${position.x + size.width/2} ${position.y + size.height/2})"` : ''
      
      switch (type) {
        case 'rect':
          return `<rect id="${node.id}" x="${position.x}" y="${position.y}" width="${size.width}" height="${size.height}" fill="${style.fill || 'transparent'}" stroke="${style.stroke || '#000'}" stroke-width="${style.strokeWidth || 1}" opacity="${style.opacity || 1}"${transform} class="node-element" data-name="${node.name}"/>`
        
        case 'circle':
          const cx = position.x + size.width / 2
          const cy = position.y + size.height / 2
          const r = Math.min(size.width, size.height) / 2
          return `<circle id="${node.id}" cx="${cx}" cy="${cy}" r="${r}" fill="${style.fill || 'transparent'}" stroke="${style.stroke || '#000'}" stroke-width="${style.strokeWidth || 1}" opacity="${style.opacity || 1}"${transform} class="node-element" data-name="${node.name}"/>`
        
        case 'line':
          return `<line id="${node.id}" x1="${position.x}" y1="${position.y + size.height/2}" x2="${position.x + size.width}" y2="${position.y + size.height/2}" stroke="${style.stroke || '#000'}" stroke-width="${style.strokeWidth || 2}" opacity="${style.opacity || 1}"${transform} class="node-element" data-name="${node.name}"/>`
        
        default:
          return `<rect id="${node.id}" x="${position.x}" y="${position.y}" width="${size.width}" height="${size.height}" fill="${style.fill || '#e0e0e0'}" stroke="${style.stroke || '#000'}" stroke-width="${style.strokeWidth || 1}" opacity="${style.opacity || 1}"${transform} class="node-element custom-node" data-name="${node.name}">
            <title>${node.name}</title>
          </rect>`
      }
    }).join('\n        ')
  }

  // 生成CSS样式
  private generateCSS(): string {
    return `
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        
        .canvas-container {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .node-element {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .node-element:hover {
            filter: brightness(1.1);
            stroke-width: 2;
        }
        
        .controls {
            text-align: center;
        }
        
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1976d2;
        }
        
        .status-indicator {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }`
  }

  // 生成JavaScript代码
  private generateJavaScript(nodes: NodeElement[], canvasState: CanvasState): string {
    const dataBindings = nodes.filter(node => node.dataBind)
    
    return `
        let isSimulating = false;
        let simulationInterval;
        
        // 数据绑定配置
        const dataBindings = ${JSON.stringify(dataBindings.map(node => ({
          nodeId: node.id,
          source: node.dataBind?.source,
          field: node.dataBind?.field
        })), null, 8)};
        
        // 模拟数据
        function generateMockData() {
            return {
                device_01: {
                    status: Math.random() > 0.8 ? 'error' : Math.random() > 0.5 ? 'warning' : 'normal',
                    temperature: Math.round(Math.random() * 100),
                    pressure: Math.round(Math.random() * 50)
                },
                device_02: {
                    status: Math.random() > 0.9 ? 'offline' : 'normal',
                    level: Math.round(Math.random() * 100)
                }
            };
        }
        
        // 更新节点样式
        function updateNodeStyle(nodeId, data) {
            const element = document.getElementById(nodeId);
            if (!element) return;
            
            if (typeof data === 'string') {
                switch (data.toLowerCase()) {
                    case 'error':
                    case 'alarm':
                        element.setAttribute('fill', '#f44336');
                        element.setAttribute('stroke', '#d32f2f');
                        break;
                    case 'warning':
                        element.setAttribute('fill', '#ff9800');
                        element.setAttribute('stroke', '#f57c00');
                        break;
                    case 'normal':
                    case 'ok':
                        element.setAttribute('fill', '#4caf50');
                        element.setAttribute('stroke', '#388e3c');
                        break;
                    case 'offline':
                        element.setAttribute('fill', '#9e9e9e');
                        element.setAttribute('stroke', '#616161');
                        element.setAttribute('opacity', '0.5');
                        break;
                }
            }
        }
        
        // 切换数据模拟
        function toggleDataSimulation() {
            isSimulating = !isSimulating;
            
            if (isSimulating) {
                simulationInterval = setInterval(() => {
                    const mockData = generateMockData();
                    
                    dataBindings.forEach(binding => {
                        const data = mockData[binding.source];
                        if (data && binding.field) {
                            const fieldValue = binding.field.split('.').reduce((obj, key) => obj?.[key], data);
                            updateNodeStyle(binding.nodeId, fieldValue);
                        }
                    });
                }, 1000);
            } else {
                clearInterval(simulationInterval);
            }
        }
        
        // 导出数据
        function exportData() {
            const data = {
                canvas: ${JSON.stringify(canvasState, null, 12)},
                exportTime: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'visual-data.json';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 节点点击事件
        document.querySelectorAll('.node-element').forEach(element => {
            element.addEventListener('click', function() {
                const name = this.getAttribute('data-name');
                alert('点击了节点: ' + name);
            });
        });
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Le5le Visual Project loaded');
        });`
  }

  // 生成Vue模板
  private generateVueTemplate(nodes: NodeElement[]): string {
    const svgElements = this.generateSVGElements(nodes)
    
    return `  <div class="visual-component">
    <svg class="canvas" width="100%" height="600" viewBox="0 0 1200 600">
      ${svgElements.split('\n').map(line => '      ' + line).join('\n')}
    </svg>
  </div>`
  }

  // 生成Vue脚本
  private generateVueScript(nodes: NodeElement[], componentName: string): string {
    return `import { ref, onMounted, onUnmounted } from 'vue'

// 组件数据
const isSimulating = ref(false)
let simulationInterval: number | null = null

// 模拟数据更新
function startSimulation() {
  isSimulating.value = true
  simulationInterval = setInterval(() => {
    // 更新数据绑定的节点
    updateBoundNodes()
  }, 1000)
}

function stopSimulation() {
  isSimulating.value = false
  if (simulationInterval) {
    clearInterval(simulationInterval)
    simulationInterval = null
  }
}

function updateBoundNodes() {
  // 实现数据更新逻辑
  console.log('Updating bound nodes...')
}

// 生命周期
onMounted(() => {
  console.log('${componentName} mounted')
})

onUnmounted(() => {
  stopSimulation()
})`
  }

  // 生成Vue样式
  private generateVueStyle(): string {
    return `.visual-component {
  width: 100%;
  height: 100%;
}

.canvas {
  border: 1px solid #ddd;
  background: white;
}

.node-element {
  cursor: pointer;
  transition: all 0.2s ease;
}

.node-element:hover {
  filter: brightness(1.1);
}`
  }

  // 生成React JSX
  private generateReactJSX(nodes: NodeElement[]): string {
    const svgElements = this.generateSVGElements(nodes)
    
    return `    <div className="visual-component">
      <svg className="canvas" width="100%" height="600" viewBox="0 0 1200 600">
        ${svgElements.split('\n').map(line => '        ' + line).join('\n')}
      </svg>
    </div>`
  }

  // 生成React Hooks
  private generateReactHooks(nodes: NodeElement[]): string {
    return `  const [isSimulating, setIsSimulating] = useState(false);
  
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    
    if (isSimulating) {
      interval = setInterval(() => {
        // 更新数据绑定的节点
        updateBoundNodes();
      }, 1000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isSimulating]);
  
  const updateBoundNodes = () => {
    // 实现数据更新逻辑
    console.log('Updating bound nodes...');
  };`
  }

  // 下载文件
  downloadFile(content: string, filename: string, mimeType: string = 'text/plain') {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}

// 创建全局实例
export const exportService = new ExportService()
