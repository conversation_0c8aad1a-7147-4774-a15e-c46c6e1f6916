// 开发工具配置和错误处理

// 禁用 Vue DevTools 的 runtime 错误
if (typeof window !== 'undefined') {
  // 捕获并忽略 Vue DevTools 相关的错误
  const originalError = console.error
  console.error = (...args: any[]) => {
    const message = args[0]
    
    // 忽略 Vue DevTools 相关的错误
    if (typeof message === 'string' && (
      message.includes('props is not string type') ||
      message.includes('runtime.lastError') ||
      message.includes('Extension context invalidated')
    )) {
      return
    }
    
    // 其他错误正常输出
    originalError.apply(console, args)
  }
  
  // 处理未捕获的错误
  window.addEventListener('error', (event) => {
    const message = event.message
    
    // 忽略 Vue DevTools 相关的错误
    if (message && (
      message.includes('props is not string type') ||
      message.includes('runtime.lastError') ||
      message.includes('Extension context invalidated')
    )) {
      event.preventDefault()
      return false
    }
  })
  
  // 处理未捕获的 Promise 错误
  window.addEventListener('unhandledrejection', (event) => {
    const message = event.reason?.message || event.reason
    
    // 忽略 Vue DevTools 相关的错误
    if (typeof message === 'string' && (
      message.includes('props is not string type') ||
      message.includes('runtime.lastError') ||
      message.includes('Extension context invalidated')
    )) {
      event.preventDefault()
      return false
    }
  })
}

export {}
