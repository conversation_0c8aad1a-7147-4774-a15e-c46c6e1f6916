<template>
  <div class="canvas-container" ref="containerRef">
    <div 
      class="canvas-wrapper"
      :style="canvasWrapperStyle"
      @wheel="handleWheel"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @contextmenu.prevent
    >
      <!-- 网格背景 -->
      <div v-if="canvasStore.showGrid" class="grid-background" :style="gridStyle"></div>
      
      <!-- 图元渲染区域 -->
      <div class="nodes-layer">
        <NodeElement
          v-for="node in canvasStore.nodes"
          :key="node.id"
          :node="node"
          :selected="canvasStore.selectedNodeIds.includes(node.id)"
          @select="handleNodeSelect"
          @update="handleNodeUpdate"
        />
      </div>
      
      <!-- 选择框 -->
      <div 
        v-if="selectionBox"
        class="selection-box"
        :style="selectionBoxStyle"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import NodeElement from './NodeElement.vue'
import type { Position, NodeElement as NodeType } from '@/types'

const canvasStore = useCanvasStore()
const containerRef = ref<HTMLElement>()

// 交互状态
const isDragging = ref(false)
const isSelecting = ref(false)
const dragStart = ref<Position>({ x: 0, y: 0 })
const selectionBox = ref<{ start: Position; end: Position } | null>(null)

// 样式计算
const canvasWrapperStyle = computed(() => ({
  transform: `scale(${canvasStore.scale}) translate(${canvasStore.offset.x}px, ${canvasStore.offset.y}px)`,
  transformOrigin: '0 0'
}))

const gridStyle = computed(() => ({
  backgroundImage: `
    linear-gradient(to right, #e0e0e0 1px, transparent 1px),
    linear-gradient(to bottom, #e0e0e0 1px, transparent 1px)
  `,
  backgroundSize: `${canvasStore.gridSize}px ${canvasStore.gridSize}px`
}))

const selectionBoxStyle = computed(() => {
  if (!selectionBox.value) return {}
  const { start, end } = selectionBox.value
  return {
    left: `${Math.min(start.x, end.x)}px`,
    top: `${Math.min(start.y, end.y)}px`,
    width: `${Math.abs(end.x - start.x)}px`,
    height: `${Math.abs(end.y - start.y)}px`
  }
})

// 事件处理
function handleWheel(event: WheelEvent) {
  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  canvasStore.setScale(canvasStore.scale + delta)
}

function handleMouseDown(event: MouseEvent) {
  if (event.button === 0) { // 左键
    const rect = containerRef.value?.getBoundingClientRect()
    if (!rect) return
    
    dragStart.value = {
      x: (event.clientX - rect.left) / canvasStore.scale - canvasStore.offset.x,
      y: (event.clientY - rect.top) / canvasStore.scale - canvasStore.offset.y
    }
    
    if (event.ctrlKey || event.metaKey) {
      // 开始选择框
      isSelecting.value = true
      selectionBox.value = {
        start: dragStart.value,
        end: dragStart.value
      }
    } else {
      // 开始拖拽画布
      isDragging.value = true
    }
  }
}

function handleMouseMove(event: MouseEvent) {
  if (!containerRef.value) return
  
  const rect = containerRef.value.getBoundingClientRect()
  const currentPos = {
    x: (event.clientX - rect.left) / canvasStore.scale - canvasStore.offset.x,
    y: (event.clientY - rect.top) / canvasStore.scale - canvasStore.offset.y
  }
  
  if (isSelecting.value && selectionBox.value) {
    selectionBox.value.end = currentPos
  } else if (isDragging.value) {
    const deltaX = currentPos.x - dragStart.value.x
    const deltaY = currentPos.y - dragStart.value.y
    canvasStore.setOffset({
      x: canvasStore.offset.x + deltaX,
      y: canvasStore.offset.y + deltaY
    })
  }
}

function handleMouseUp() {
  if (isSelecting.value && selectionBox.value) {
    // 处理选择框选择
    const { start, end } = selectionBox.value
    const minX = Math.min(start.x, end.x)
    const maxX = Math.max(start.x, end.x)
    const minY = Math.min(start.y, end.y)
    const maxY = Math.max(start.y, end.y)
    
    const selectedIds = canvasStore.nodes
      .filter(node => 
        node.position.x >= minX && 
        node.position.x + node.size.width <= maxX &&
        node.position.y >= minY && 
        node.position.y + node.size.height <= maxY
      )
      .map(node => node.id)
    
    canvasStore.selectedNodeIds = selectedIds
  }
  
  isDragging.value = false
  isSelecting.value = false
  selectionBox.value = null
}

function handleNodeSelect(nodeId: string, multiple: boolean) {
  canvasStore.selectNode(nodeId, multiple)
}

function handleNodeUpdate(nodeId: string, updates: Partial<NodeType>) {
  canvasStore.updateNode(nodeId, updates)
}

// 键盘事件
function handleKeyDown(event: KeyboardEvent) {
  if (event.key === 'Delete' || event.key === 'Backspace') {
    canvasStore.selectedNodeIds.forEach(id => {
      canvasStore.deleteNode(id)
    })
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.canvas-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background: #f5f5f5;
  cursor: grab;
}

.canvas-container:active {
  cursor: grabbing;
}

.canvas-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.nodes-layer {
  position: relative;
  width: 100%;
  height: 100%;
}

.selection-box {
  position: absolute;
  border: 2px dashed #007bff;
  background: rgba(0, 123, 255, 0.1);
  pointer-events: none;
}
</style>
