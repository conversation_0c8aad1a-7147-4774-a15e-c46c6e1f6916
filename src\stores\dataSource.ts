import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { DataSource } from '@/types'

export const useDataSourceStore = defineStore('dataSource', () => {
  const dataSources = ref<DataSource[]>([])
  const connections = ref<Map<string, WebSocket>>(new Map())
  const dataCache = ref<Map<string, any>>(new Map())

  function addDataSource(dataSource: DataSource) {
    dataSources.value.push(dataSource)
  }

  function removeDataSource(id: string) {
    const index = dataSources.value.findIndex(ds => ds.id === id)
    if (index !== -1) {
      // 关闭连接
      const connection = connections.value.get(id)
      if (connection) {
        connection.close()
        connections.value.delete(id)
      }
      dataSources.value.splice(index, 1)
    }
  }

  function connectWebSocket(dataSource: DataSource) {
    if (dataSource.type !== 'websocket') return

    const ws = new WebSocket(dataSource.url)
    
    ws.onopen = () => {
      console.log(`WebSocket connected: ${dataSource.name}`)
      connections.value.set(dataSource.id, ws)
    }

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        dataCache.value.set(dataSource.id, data)
        // 触发数据更新事件
        window.dispatchEvent(new CustomEvent('dataUpdate', {
          detail: { sourceId: dataSource.id, data }
        }))
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    }

    ws.onclose = () => {
      console.log(`WebSocket disconnected: ${dataSource.name}`)
      connections.value.delete(dataSource.id)
    }

    ws.onerror = (error) => {
      console.error(`WebSocket error for ${dataSource.name}:`, error)
    }
  }

  function getData(sourceId: string, field?: string) {
    const data = dataCache.value.get(sourceId)
    if (!data) return null
    
    if (field) {
      return field.split('.').reduce((obj, key) => obj?.[key], data)
    }
    return data
  }

  function sendData(sourceId: string, data: any) {
    const connection = connections.value.get(sourceId)
    if (connection && connection.readyState === WebSocket.OPEN) {
      connection.send(JSON.stringify(data))
    }
  }

  return {
    dataSources,
    connections,
    dataCache,
    addDataSource,
    removeDataSource,
    connectWebSocket,
    getData,
    sendData
  }
})
