<template>
  <div
    class="node-element"
    :class="{ selected }"
    :style="nodeStyle"
    @mousedown.stop="handleMouseDown"
    @click.stop="handleClick"
  >
    <!-- 矩形 -->
    <div v-if="node.type === 'rect'" class="rect-node" :style="shapeStyle"></div>
    
    <!-- 圆形 -->
    <div v-else-if="node.type === 'circle'" class="circle-node" :style="shapeStyle"></div>
    
    <!-- 线条 -->
    <svg v-else-if="node.type === 'line'" class="line-node" :style="shapeStyle">
      <line 
        x1="0" 
        y1="50%" 
        x2="100%" 
        y2="50%" 
        :stroke="node.style.stroke || '#000'"
        :stroke-width="node.style.strokeWidth || 2"
      />
    </svg>
    
    <!-- 自定义节点 -->
    <div v-else class="custom-node" :style="shapeStyle">
      {{ node.name }}
    </div>
    
    <!-- 选中时的控制点 -->
    <div v-if="selected" class="control-points">
      <div class="control-point nw" @mousedown.stop="handleResize('nw', $event)"></div>
      <div class="control-point ne" @mousedown.stop="handleResize('ne', $event)"></div>
      <div class="control-point sw" @mousedown.stop="handleResize('sw', $event)"></div>
      <div class="control-point se" @mousedown.stop="handleResize('se', $event)"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { eventService } from '@/services/EventService'
import type { NodeElement, Position } from '@/types'

interface Props {
  node: NodeElement
  selected: boolean
}

interface Emits {
  (e: 'select', nodeId: string, multiple: boolean): void
  (e: 'update', nodeId: string, updates: Partial<NodeElement>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isDragging = ref(false)
const isResizing = ref(false)
const dragStart = ref<Position>({ x: 0, y: 0 })
const resizeDirection = ref<string>('')

// 样式计算
const nodeStyle = computed(() => ({
  position: 'absolute',
  left: `${props.node.position.x}px`,
  top: `${props.node.position.y}px`,
  width: `${props.node.size.width}px`,
  height: `${props.node.size.height}px`,
  transform: `rotate(${props.node.rotation || 0}deg)`,
  zIndex: props.node.zIndex,
  opacity: props.node.style.opacity || 1,
  cursor: isDragging.value ? 'grabbing' : 'grab'
}))

const shapeStyle = computed(() => ({
  width: '100%',
  height: '100%',
  backgroundColor: props.node.style.fill || 'transparent',
  border: `${props.node.style.strokeWidth || 1}px solid ${props.node.style.stroke || '#000'}`,
  borderRadius: props.node.type === 'circle' ? '50%' : '0'
}))

// 事件处理
function handleClick(event: MouseEvent) {
  emit('select', props.node.id, event.ctrlKey || event.metaKey)

  // 触发节点点击事件
  eventService.handleNodeClick(props.node, event)
}

function handleMouseDown(event: MouseEvent) {
  if (event.button === 0) {
    isDragging.value = true
    dragStart.value = {
      x: event.clientX - props.node.position.x,
      y: event.clientY - props.node.position.y
    }
    
    document.addEventListener('mousemove', handleDragMove)
    document.addEventListener('mouseup', handleDragEnd)
  }
}

function handleDragMove(event: MouseEvent) {
  if (isDragging.value) {
    const newPosition = {
      x: event.clientX - dragStart.value.x,
      y: event.clientY - dragStart.value.y
    }
    emit('update', props.node.id, { position: newPosition })
  }
}

function handleDragEnd() {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
}

function handleResize(direction: string, event: MouseEvent) {
  isResizing.value = true
  resizeDirection.value = direction
  dragStart.value = { x: event.clientX, y: event.clientY }
  
  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
}

function handleResizeMove(event: MouseEvent) {
  if (!isResizing.value) return
  
  const deltaX = event.clientX - dragStart.value.x
  const deltaY = event.clientY - dragStart.value.y
  
  const updates: Partial<NodeElement> = {}
  
  switch (resizeDirection.value) {
    case 'se':
      updates.size = {
        width: Math.max(20, props.node.size.width + deltaX),
        height: Math.max(20, props.node.size.height + deltaY)
      }
      break
    case 'sw':
      updates.size = {
        width: Math.max(20, props.node.size.width - deltaX),
        height: Math.max(20, props.node.size.height + deltaY)
      }
      updates.position = {
        x: props.node.position.x + deltaX,
        y: props.node.position.y
      }
      break
    case 'ne':
      updates.size = {
        width: Math.max(20, props.node.size.width + deltaX),
        height: Math.max(20, props.node.size.height - deltaY)
      }
      updates.position = {
        x: props.node.position.x,
        y: props.node.position.y + deltaY
      }
      break
    case 'nw':
      updates.size = {
        width: Math.max(20, props.node.size.width - deltaX),
        height: Math.max(20, props.node.size.height - deltaY)
      }
      updates.position = {
        x: props.node.position.x + deltaX,
        y: props.node.position.y + deltaY
      }
      break
  }
  
  if (Object.keys(updates).length > 0) {
    emit('update', props.node.id, updates)
    dragStart.value = { x: event.clientX, y: event.clientY }
  }
}

function handleResizeEnd() {
  isResizing.value = false
  resizeDirection.value = ''
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
}
</script>

<style scoped>
.node-element {
  position: absolute;
  user-select: none;
}

.node-element.selected {
  outline: 2px solid #007bff;
}

.rect-node, .circle-node, .custom-node {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.line-node {
  width: 100%;
  height: 100%;
}

.control-points {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  pointer-events: none;
}

.control-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #007bff;
  border: 1px solid #fff;
  border-radius: 50%;
  pointer-events: all;
  cursor: pointer;
}

.control-point.nw {
  top: 0;
  left: 0;
  cursor: nw-resize;
}

.control-point.ne {
  top: 0;
  right: 0;
  cursor: ne-resize;
}

.control-point.sw {
  bottom: 0;
  left: 0;
  cursor: sw-resize;
}

.control-point.se {
  bottom: 0;
  right: 0;
  cursor: se-resize;
}
</style>
