<template>
  <div class="editor-view">
    <!-- 顶部工具栏 -->
    <div class="top-toolbar">
      <div class="toolbar-left">
        <button @click="newProject" class="toolbar-btn">新建</button>
        <button @click="saveProject" class="toolbar-btn">保存</button>
        <button @click="exportProject" class="toolbar-btn">导出</button>
        <input 
          ref="importInput" 
          type="file" 
          accept=".json" 
          style="display: none"
          @change="importProject"
        >
        <button @click="$refs.importInput.click()" class="toolbar-btn">导入</button>
      </div>
      
      <div class="toolbar-center">
        <span class="project-title">{{ projectName }}</span>
      </div>
      
      <div class="toolbar-right">
        <button @click="undo" :disabled="!canUndo" class="toolbar-btn">撤销</button>
        <button @click="redo" :disabled="!canRedo" class="toolbar-btn">重做</button>
        <button @click="toggleGrid" class="toolbar-btn" :class="{ active: canvasStore.showGrid }">
          网格
        </button>
        <div class="zoom-controls">
          <button @click="zoomOut" class="zoom-btn">-</button>
          <span class="zoom-level">{{ Math.round(canvasStore.scale * 100) }}%</span>
          <button @click="zoomIn" class="zoom-btn">+</button>
        </div>
      </div>
    </div>
    
    <!-- 主编辑区域 -->
    <div class="editor-main">
      <!-- 左侧图元库 -->
      <NodeLibrary />
      
      <!-- 中间画布区域 -->
      <div class="canvas-area" @drop="handleDrop" @dragover.prevent>
        <CanvasContainer />
      </div>
      
      <!-- 右侧面板 -->
      <div class="right-panel">
        <PropertyPanel />
        <DemoData />
      </div>
    </div>
    
    <!-- 底部状态栏 -->
    <div class="status-bar">
      <span>节点数: {{ canvasStore.nodes.length }}</span>
      <span>已选择: {{ canvasStore.selectedNodeIds.length }}</span>
      <span>缩放: {{ Math.round(canvasStore.scale * 100) }}%</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { exportService } from '@/services/ExportService'
import CanvasContainer from '@/components/Canvas/CanvasContainer.vue'
import NodeLibrary from '@/components/Toolbar/NodeLibrary.vue'
import PropertyPanel from '@/components/Properties/PropertyPanel.vue'
import DemoData from '@/components/Demo/DemoData.vue'
import type { NodeElement } from '@/types'

const canvasStore = useCanvasStore()
const projectName = ref('未命名项目')
const importInput = ref<HTMLInputElement>()

// 历史记录（简单实现）
const history = ref<string[]>([])
const historyIndex = ref(-1)
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// 保存历史状态
function saveHistory() {
  const state = canvasStore.exportToJSON()
  history.value = history.value.slice(0, historyIndex.value + 1)
  history.value.push(state)
  historyIndex.value = history.value.length - 1
  
  // 限制历史记录数量
  if (history.value.length > 50) {
    history.value.shift()
    historyIndex.value--
  }
}

// 撤销
function undo() {
  if (canUndo.value) {
    historyIndex.value--
    const state = history.value[historyIndex.value]
    canvasStore.importFromJSON(state)
  }
}

// 重做
function redo() {
  if (canRedo.value) {
    historyIndex.value++
    const state = history.value[historyIndex.value]
    canvasStore.importFromJSON(state)
  }
}

// 项目操作
function newProject() {
  if (confirm('确定要新建项目吗？当前项目将被清空。')) {
    canvasStore.nodes = []
    canvasStore.selectedNodeIds = []
    canvasStore.setScale(1)
    canvasStore.setOffset({ x: 0, y: 0 })
    projectName.value = '未命名项目'
    saveHistory()
  }
}

function saveProject() {
  const projectData = {
    name: projectName.value,
    canvas: canvasStore.canvasState,
    createdAt: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(projectData, null, 2)], { 
    type: 'application/json' 
  })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${projectName.value}.json`
  a.click()
  URL.revokeObjectURL(url)
}

function exportProject() {
  // 显示导出选项
  const format = prompt('选择导出格式:\n1. HTML\n2. Vue组件\n3. React组件\n4. JSON配置', '1')

  switch (format) {
    case '1':
      const htmlContent = exportService.exportToHTML(projectName.value)
      exportService.downloadFile(htmlContent, `${projectName.value}.html`, 'text/html')
      break
    case '2':
      const vueContent = exportService.exportToVueComponent('VisualComponent')
      exportService.downloadFile(vueContent, `${projectName.value}.vue`, 'text/plain')
      break
    case '3':
      const reactContent = exportService.exportToReactComponent('VisualComponent')
      exportService.downloadFile(reactContent, `${projectName.value}.tsx`, 'text/plain')
      break
    case '4':
      const jsonContent = exportService.exportToJSON()
      exportService.downloadFile(jsonContent, `${projectName.value}.json`, 'application/json')
      break
    default:
      // 默认导出HTML
      const defaultContent = exportService.exportToHTML(projectName.value)
      exportService.downloadFile(defaultContent, `${projectName.value}.html`, 'text/html')
  }
}

function importProject(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const projectData = JSON.parse(e.target?.result as string)
      canvasStore.importFromJSON(JSON.stringify(projectData.canvas))
      projectName.value = projectData.name || '导入的项目'
      saveHistory()
    } catch (error) {
      alert('导入失败：文件格式错误')
    }
  }
  reader.readAsText(file)
}

// 缩放控制
function zoomIn() {
  canvasStore.setScale(canvasStore.scale + 0.1)
}

function zoomOut() {
  canvasStore.setScale(canvasStore.scale - 0.1)
}

function toggleGrid() {
  canvasStore.showGrid = !canvasStore.showGrid
}

// 拖拽处理
function handleDrop(event: DragEvent) {
  event.preventDefault()
  const data = event.dataTransfer?.getData('application/json')
  if (!data) return
  
  try {
    const template = JSON.parse(data)
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const x = (event.clientX - rect.left) / canvasStore.scale - canvasStore.offset.x
    const y = (event.clientY - rect.top) / canvasStore.scale - canvasStore.offset.y
    
    const newNode: Omit<NodeElement, 'id'> = {
      name: template.name,
      type: template.type,
      position: { x, y },
      size: template.defaultSize,
      style: template.defaultStyle,
      zIndex: canvasStore.nodes.length + 1
    }
    
    canvasStore.addNode(newNode)
    saveHistory()
  } catch (error) {
    console.error('Drop failed:', error)
  }
}

// 生成HTML导出
function generateHTMLExport(): string {
  const nodes = canvasStore.nodes
  const svgElements = nodes.map(node => {
    const { position, size, style, type } = node
    
    let element = ''
    switch (type) {
      case 'rect':
        element = `<rect x="${position.x}" y="${position.y}" width="${size.width}" height="${size.height}" fill="${style.fill}" stroke="${style.stroke}" stroke-width="${style.strokeWidth}"/>`
        break
      case 'circle':
        const cx = position.x + size.width / 2
        const cy = position.y + size.height / 2
        const r = Math.min(size.width, size.height) / 2
        element = `<circle cx="${cx}" cy="${cy}" r="${r}" fill="${style.fill}" stroke="${style.stroke}" stroke-width="${style.strokeWidth}"/>`
        break
      case 'line':
        element = `<line x1="${position.x}" y1="${position.y + size.height/2}" x2="${position.x + size.width}" y2="${position.y + size.height/2}" stroke="${style.stroke}" stroke-width="${style.strokeWidth}"/>`
        break
    }
    return element
  }).join('\n    ')
  
  return `<!DOCTYPE html>
<html>
<head>
    <title>${projectName.value}</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        .canvas { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>${projectName.value}</h1>
    <svg class="canvas" width="800" height="600" viewBox="0 0 800 600">
        ${svgElements}
    </svg>
</body>
</html>`
}

// 键盘快捷键
function handleKeyDown(event: KeyboardEvent) {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'z':
        event.preventDefault()
        if (event.shiftKey) {
          redo()
        } else {
          undo()
        }
        break
      case 's':
        event.preventDefault()
        saveProject()
        break
      case 'n':
        event.preventDefault()
        newProject()
        break
    }
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
  saveHistory() // 初始状态
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.editor-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.top-toolbar {
  height: 48px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 0 16px;
  justify-content: space-between;
}

.toolbar-left, .toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background: #f5f5f5;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn.active {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

.project-title {
  font-weight: 500;
  color: #333;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 4px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
}

.zoom-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-level {
  padding: 0 8px;
  font-size: 12px;
  min-width: 40px;
  text-align: center;
}

.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.canvas-area {
  flex: 1;
  position: relative;
}

.right-panel {
  width: 300px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-left: 1px solid #e0e0e0;
  overflow-y: auto;
}

.status-bar {
  height: 24px;
  background: #fff;
  border-top: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 0 16px;
  gap: 16px;
  font-size: 12px;
  color: #666;
}
</style>
