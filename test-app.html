<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Le5le Visual Platform Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background: #e8f5e8;
            border-color: #4caf50;
        }
        .error {
            background: #ffeaea;
            border-color: #f44336;
        }
        .info {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Le5le Visual Platform 测试页面</h1>
        
        <div class="test-item info">
            <h3>项目状态检查</h3>
            <p>这个页面用于测试 Le5le Visual Platform 是否正常运行。</p>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="openMainApp()">打开主应用</button>
        </div>
        
        <div id="test-results"></div>
        
        <div class="test-item">
            <h3>功能说明</h3>
            <ul>
                <li>✅ Pinia 状态管理已修复</li>
                <li>✅ Vue 3 Composition API 语法已更新</li>
                <li>✅ TypeScript 类型声明已添加</li>
                <li>✅ 服务类的 Store 初始化问题已解决</li>
            </ul>
        </div>
        
        <div class="test-item">
            <h3>主要功能</h3>
            <ul>
                <li>🎨 可视化画布编辑器</li>
                <li>📦 图元库（矩形、圆形、线条等）</li>
                <li>⚙️ 属性面板</li>
                <li>🔗 数据绑定</li>
                <li>📤 导出功能（JSON、HTML、Vue、React）</li>
                <li>🎯 事件处理</li>
                <li>📊 演示数据</li>
            </ul>
        </div>
    </div>

    <script>
        function testConnection() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="test-item info">正在测试连接...</div>';
            
            fetch('http://localhost:5174/')
                .then(response => {
                    if (response.ok) {
                        resultsDiv.innerHTML = '<div class="test-item success"><h3>✅ 连接成功</h3><p>开发服务器运行正常，端口: 5174</p></div>';
                    } else {
                        resultsDiv.innerHTML = '<div class="test-item error"><h3>❌ 连接失败</h3><p>HTTP状态: ' + response.status + '</p></div>';
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<div class="test-item error"><h3>❌ 连接错误</h3><p>错误信息: ' + error.message + '</p></div>';
                });
        }
        
        function openMainApp() {
            window.open('http://localhost:5174/', '_blank');
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
